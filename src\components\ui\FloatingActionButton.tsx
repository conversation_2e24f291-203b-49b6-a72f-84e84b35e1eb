"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ArrowUp, MessageCircle, Phone } from "lucide-react";

interface FloatingActionButtonProps {
  showScrollTop?: boolean;
  showChat?: boolean;
  showCall?: boolean;
  onChatClick?: () => void;
  onCallClick?: () => void;
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  showScrollTop = true,
  showChat = true,
  showCall = true,
  onChatClick,
  onCallClick,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  useEffect(() => {
    const toggleVisibility = () => {
      if (window.pageYOffset > 300) {
        setIsVisible(true);
      } else {
        setIsVisible(false);
        setIsExpanded(false);
      }
    };

    window.addEventListener("scroll", toggleVisibility);
    return () => window.removeEventListener("scroll", toggleVisibility);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  const actions = [
    {
      icon: ArrowUp,
      label: "Scroll to top",
      onClick: scrollToTop,
      show: showScrollTop,
      color: "bg-primary-600 hover:bg-primary-700",
    },
    {
      icon: MessageCircle,
      label: "Chat with us",
      onClick: onChatClick,
      show: showChat,
      color: "bg-green-600 hover:bg-green-700",
    },
    {
      icon: Phone,
      label: "Call us",
      onClick: onCallClick,
      show: showCall,
      color: "bg-blue-600 hover:bg-blue-700",
    },
  ].filter((action) => action.show);

  return (
    <AnimatePresence>
      {isVisible && (
        <div className="fixed bottom-6 right-6 z-50">
          <div className="flex flex-col items-end space-y-3">
            {/* Action Buttons */}
            <AnimatePresence>
              {isExpanded && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.8 }}
                  transition={{ duration: 0.2 }}
                  className="flex flex-col space-y-3"
                >
                  {actions.slice(1).map((action, index) => (
                    <motion.button
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                      transition={{ duration: 0.2, delay: index * 0.1 }}
                      onClick={action.onClick}
                      className={`w-12 h-12 ${action.color} text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center group`}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      {React.createElement(action.icon, {
                        className: "w-5 h-5",
                      })}
                      <span className="absolute right-14 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                        {action.label}
                      </span>
                    </motion.button>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>

            {/* Main Button */}
            <motion.button
              initial={{ opacity: 0, scale: 0 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0 }}
              transition={{ duration: 0.3 }}
              onClick={() => {
                if (actions.length === 1) {
                  actions[0].onClick?.();
                } else {
                  setIsExpanded(!isExpanded);
                }
              }}
              className={`w-14 h-14 ${actions[0].color} text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <motion.div
                animate={{ rotate: isExpanded ? 45 : 0 }}
                transition={{ duration: 0.2 }}
              >
                {actions.length === 1 ? (
                  React.createElement(actions[0].icon, { className: "w-6 h-6" })
                ) : (
                  <div className="w-6 h-6 flex items-center justify-center">
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                    <div className="w-1 h-1 bg-white rounded-full mx-1"></div>
                    <div className="w-1 h-1 bg-white rounded-full"></div>
                  </div>
                )}
              </motion.div>
            </motion.button>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default FloatingActionButton;
