@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 255, 255, 255;
  --background-end-rgb: 255, 255, 255;
  --gold: #d4af37;
  --gold-light: #f4e5a3;
  --gold-dark: #b8941f;
  --navy: #1e3a8a;
  --navy-light: #3b82f6;
  --navy-dark: #1e40af;

  /* Enhanced color variables */
  --primary-50: #fefce8;
  --primary-500: #eab308;
  --primary-600: #ca8a04;
  --secondary-50: #f8fafc;
  --secondary-900: #0f172a;
  --accent-500: #3b82f6;

  /* Glass morphism variables */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* Gradient variables */
  --gradient-primary: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  --gradient-accent: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
  --gradient-mesh: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* Enhanced Form Styles */
.form-input {
  transition: all 0.2s ease-in-out;
}

.form-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(212, 175, 55, 0.2);
}

/* Mobile Menu Button - Ensure visibility on mobile */
@media (max-width: 1023px) {
  .lg\:hidden {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }
}

/* Ensure form elements have proper text color regardless of system color scheme */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="number"],
input[type="url"],
input[type="search"],
select,
textarea {
  color: #111827; /* text-gray-900 equivalent */
}

/* Ensure placeholder text is visible */
input::placeholder,
textarea::placeholder {
  color: #9ca3af; /* text-gray-400 equivalent */
}

/* Ensure select options have proper contrast */
select option {
  color: #111827;
  background-color: #ffffff;
}

.form-section {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0.85)
  );
  backdrop-filter: blur(10px);
}

.progress-bar {
  background: linear-gradient(90deg, #fbbf24, #f59e0b);
  box-shadow: 0 0 10px rgba(251, 191, 36, 0.4);
}

.file-drop-zone {
  background: linear-gradient(
    135deg,
    rgba(249, 250, 251, 0.8),
    rgba(243, 244, 246, 0.8)
  );
  border: 2px dashed #d1d5db;
  transition: all 0.3s ease;
}

.file-drop-zone:hover {
  border-color: #fbbf24;
  background: linear-gradient(
    135deg,
    rgba(254, 243, 199, 0.8),
    rgba(253, 230, 138, 0.8)
  );
  transform: scale(1.01);
}

.file-drop-zone.active {
  border-color: #f59e0b;
  background: linear-gradient(
    135deg,
    rgba(254, 243, 199, 0.9),
    rgba(253, 230, 138, 0.9)
  );
  transform: scale(1.02);
}

.rtl-flip {
  transform: scaleX(-1);
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .rtl-flip {
  transform: scaleX(-1);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--gold);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gold-dark);
}

/* Smooth transitions */
* {
  transition: all 0.3s ease;
}

/* Gold gradient text */
.gradient-text {
  background: linear-gradient(135deg, var(--gold), var(--gold-light));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern UI Components */
@layer components {
  .glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    box-shadow: var(--glass-shadow);
  }

  .gradient-border {
    position: relative;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.05)
    );
    backdrop-filter: blur(10px);
    border-radius: 1rem;
  }

  .gradient-border::before {
    content: "";
    position: absolute;
    inset: 0;
    padding: 2px;
    background: var(--gradient-primary);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
  }

  /* Enhanced shadow effects */
  .shadow-glow {
    box-shadow: 0 0 20px rgba(212, 175, 55, 0.3);
  }

  .shadow-gold {
    box-shadow: 0 10px 40px rgba(212, 175, 55, 0.2);
  }

  /* Animated gradient backgrounds */
  .animated-gradient {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
  }

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  /* Floating animation */
  @keyframes float {
    0%,
    100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-20px);
    }
  }

  .animate-float {
    animation: float 6s ease-in-out infinite;
  }

  /* Pulse glow effect */
  @keyframes pulseGlow {
    0%,
    100% {
      box-shadow: 0 0 5px rgba(212, 175, 55, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(212, 175, 55, 0.8),
        0 0 30px rgba(212, 175, 55, 0.6);
    }
  }

  .animate-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }

  .btn-primary {
    @apply bg-gradient-to-r from-primary-400 to-primary-600 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-out;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-900 font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 ease-out border border-secondary-300;
  }

  .btn-ghost {
    @apply bg-transparent border-2 border-white/20 text-white font-semibold py-3 px-6 rounded-xl backdrop-blur-sm hover:bg-white/10 hover:border-white/30 transition-all duration-300 ease-out;
  }

  .card-hover {
    @apply transition-all duration-300 ease-out hover:shadow-2xl hover:-translate-y-2 hover:scale-[1.02];
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 to-primary-600 bg-clip-text text-transparent;
  }

  .text-gradient-secondary {
    @apply bg-gradient-to-r from-accent-400 to-accent-600 bg-clip-text text-transparent;
  }

  .floating-element {
    animation: float 3s ease-in-out infinite;
  }

  .shimmer-effect {
    position: relative;
    overflow: hidden;
  }

  .shimmer-effect::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: shimmer 2s infinite;
  }

  .parallax-bg {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .mesh-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-size: 400% 400%;
    animation: gradient 15s ease infinite;
  }
}

/* Custom utilities */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .perspective-1000 {
    perspective: 1000px;
  }

  .transform-style-3d {
    transform-style: preserve-3d;
  }

  .backface-hidden {
    backface-visibility: hidden;
  }

  .rotate-y-180 {
    transform: rotateY(180deg);
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Enhanced Animation classes */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.slide-in-left {
  animation: slideInLeft 0.8s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

.scale-in {
  animation: scaleIn 0.5s ease-out;
}

.bounce-in {
  animation: bounceIn 0.8s ease-out;
}

.rotate-in {
  animation: rotateIn 0.6s ease-out;
}

/* Enhanced Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes rotateIn {
  from {
    transform: rotate(-200deg);
    opacity: 0;
  }
  to {
    transform: rotate(0);
    opacity: 1;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Enhanced Shimmer Effect for Buttons */
.shimmer-effect {
  position: relative;
  overflow: hidden;
}

.shimmer-effect::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  background-size: 200% 100%;
  animation: shimmer-sweep 2s infinite;
  pointer-events: none;
  border-radius: inherit;
}

@keyframes shimmer-sweep {
  0% {
    background-position: -200% center;
  }
  100% {
    background-position: 200% center;
  }
}

/* Mesh Gradient Animation */
.mesh-gradient {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  background-size: 400% 400%;
  animation: gradient 8s ease infinite;
}
