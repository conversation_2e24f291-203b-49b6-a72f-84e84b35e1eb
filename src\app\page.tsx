"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { motion } from "framer-motion";
import Link from "next/link";
import {
  Building2,
  TrendingUp,
  Shield,
  Video,
  ArrowRight,
  CheckCircle,
  Star,
  Users,
  Award,
  Target,
  Sparkles,
  Zap,
  Globe,
} from "lucide-react";
import Button from "@/components/ui/Button";
import Card from "@/components/ui/Card";
import AnimatedBackground from "@/components/ui/AnimatedBackground";

const HomePage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const services = [
    {
      icon: Building2,
      title: t("services.brokerage.title"),
      description: t("services.brokerage.description"),
      color: "from-blue-500 to-blue-600",
      link: "/services#brokerage",
    },
    {
      icon: Shield,
      title: t("services.assets.title"),
      description: t("services.assets.description"),
      color: "from-green-500 to-green-600",
      link: "/services#assets",
    },
    {
      icon: TrendingUp,
      title: t("services.investments.title"),
      description: t("services.investments.description"),
      color: "from-purple-500 to-purple-600",
      link: "/services#investments",
    },
    {
      icon: Video,
      title: t("services.media.title"),
      description: t("services.media.description"),
      color: "from-red-500 to-red-600",
      link: "/services#media",
    },
  ];

  const stats = [
    { number: "500+", label: isRTL ? "عميل راضٍ" : "Happy Clients" },
    { number: "1000+", label: isRTL ? "مشروع مكتمل" : "Projects Completed" },
    { number: "15+", label: isRTL ? "سنة خبرة" : "Years Experience" },
    { number: "50+", label: isRTL ? "خبير متخصص" : "Expert Team" },
  ];

  const features = [
    isRTL ? "خبرة موثوقة في السوق" : "Trusted Market Expertise",
    isRTL ? "حلول مبتكرة ومخصصة" : "Innovative Custom Solutions",
    isRTL ? "دعم متواصل على مدار الساعة" : "24/7 Professional Support",
    isRTL ? "نتائج مضمونة وموثوقة" : "Guaranteed Results",
  ];

  return (
    <div className="overflow-hidden">
      {/* Enhanced Hero Section */}
      <section className="relative min-h-screen flex items-center bg-gradient-to-br from-gray-900 via-navy-800 to-secondary-900 overflow-hidden">
        {/* Animated Background */}
        <AnimatedBackground variant="particles" />

        {/* Background Image with Overlay */}
        <div className="absolute inset-0 bg-black/60"></div>
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-40"
          style={{
            backgroundImage: 'url("/Images/home_hero_section.png")',
          }}
        ></div>

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary-900/20 via-transparent to-accent-900/20"></div>

        <div className="relative container mx-auto px-4 text-white z-10">
          <div className="max-w-5xl">
            {/* Floating Badge */}
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-4 py-2 mb-6"
            >
              <Sparkles className="w-4 h-4 text-primary-400" />
              <span className="text-sm font-medium">
                {isRTL
                  ? "الرائدون في الحلول التجارية"
                  : "Leading Business Solutions"}
              </span>
            </motion.div>

            {/* Enhanced Title */}
            <motion.h1
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.1 }}
              className="text-5xl md:text-7xl lg:text-8xl font-bold mb-6 leading-tight"
            >
              <span className="block text-gradient-secondary">
                {t("hero.title")}
              </span>
            </motion.h1>

            {/* Enhanced Subtitle */}
            <motion.p
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
              className="text-xl md:text-2xl lg:text-3xl mb-8 text-gray-200 leading-relaxed max-w-3xl"
            >
              {t("hero.subtitle")}
            </motion.p>

            {/* Stats Row */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-wrap gap-6 mb-10"
            >
              {[
                {
                  icon: Users,
                  number: "500+",
                  label: isRTL ? "عميل" : "Clients",
                },
                {
                  icon: Award,
                  number: "15+",
                  label: isRTL ? "سنة خبرة" : "Years",
                },
                {
                  icon: Target,
                  number: "1000+",
                  label: isRTL ? "مشروع" : "Projects",
                },
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.5, delay: 0.5 + index * 0.1 }}
                  className="flex items-center space-x-3 rtl:space-x-reverse bg-white/10 backdrop-blur-sm rounded-2xl px-4 py-3 border border-white/20"
                >
                  <stat.icon className="w-6 h-6 text-primary-400" />
                  <div>
                    <div className="text-2xl font-bold text-white">
                      {stat.number}
                    </div>
                    <div className="text-sm text-gray-300">{stat.label}</div>
                  </div>
                </motion.div>
              ))}
            </motion.div>

            {/* Enhanced CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="flex flex-col sm:flex-row gap-4"
            >
              <Button
                variant="primary"
                size="lg"
                icon={ArrowRight}
                iconPosition="right"
                href="/contact"
                className="shimmer-effect"
              >
                {t("hero.cta")}
              </Button>

              <Button
                variant="ghost"
                size="lg"
                icon={Globe}
                iconPosition="left"
                href="/about"
              >
                {t("common.learnMore")}
              </Button>
            </motion.div>
          </div>
        </div>

        {/* Enhanced Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1.2 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="w-8 h-12 border-2 border-white/50 rounded-full flex justify-center backdrop-blur-sm"
          >
            <motion.div
              animate={{ y: [0, 16, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="w-1 h-4 bg-primary-400 rounded-full mt-2"
            ></motion.div>
          </motion.div>
        </motion.div>

        {/* Floating Elements */}
        <motion.div
          animate={{ y: [0, -20, 0], rotate: [0, 5, 0] }}
          transition={{ duration: 6, repeat: Infinity }}
          className="absolute top-20 right-20 w-20 h-20 bg-primary-400/20 rounded-full blur-xl"
        ></motion.div>
        <motion.div
          animate={{ y: [0, 20, 0], rotate: [0, -5, 0] }}
          transition={{ duration: 8, repeat: Infinity }}
          className="absolute bottom-40 left-20 w-32 h-32 bg-accent-400/20 rounded-full blur-xl"
        ></motion.div>
      </section>

      {/* Enhanced Stats Section */}
      <section className="py-20 bg-gradient-to-r from-primary-50 via-white to-accent-50 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-r from-primary-200 to-accent-200 transform rotate-12 scale-150"></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30, scale: 0.8 }}
                whileInView={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group"
              >
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="bg-white/80 backdrop-blur-sm rounded-3xl p-6 shadow-soft hover:shadow-medium transition-all duration-300 border border-white/50"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    transition={{
                      duration: 0.8,
                      delay: index * 0.1 + 0.3,
                      type: "spring",
                      bounce: 0.4,
                    }}
                    viewport={{ once: true }}
                    className="text-4xl md:text-5xl font-bold text-gradient mb-3"
                  >
                    {stat.number}
                  </motion.div>
                  <div className="text-gray-700 font-medium text-sm md:text-base group-hover:text-gray-900 transition-colors">
                    {stat.label}
                  </div>
                </motion.div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Enhanced Services Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }}
          ></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary-100 text-primary-700 rounded-full px-4 py-2 mb-6"
            >
              <Zap className="w-4 h-4" />
              <span className="text-sm font-medium">
                {isRTL ? "خدماتنا المتميزة" : "Our Premium Services"}
              </span>
            </motion.div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              <span className="text-gradient">{t("services.title")}</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              {t("services.subtitle")}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group"
              >
                <Link href={service.link}>
                  <Card
                    variant="hover"
                    className="p-8 h-full relative overflow-hidden group-hover:shadow-glow transition-all duration-500"
                  >
                    {/* Background Gradient */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white to-gray-50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    {/* Icon Container */}
                    <div className="relative z-10">
                      <motion.div
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ duration: 0.3 }}
                        className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-2xl flex items-center justify-center mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300`}
                      >
                        <service.icon className="w-8 h-8 text-white" />
                      </motion.div>

                      {/* Service Title */}
                      <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-primary-600 transition-colors duration-300">
                        {service.title}
                      </h3>

                      {/* Service Description */}
                      <p className="text-gray-600 leading-relaxed mb-6">
                        {service.description}
                      </p>

                      {/* Learn More Link */}
                      <div className="flex items-center text-primary-600 font-medium group-hover:text-primary-700 transition-colors duration-300">
                        <span className="text-sm">
                          {isRTL ? "اعرف المزيد" : "Learn More"}
                        </span>
                        <ArrowRight
                          className={`w-4 h-4 ml-2 rtl:ml-0 rtl:mr-2 transform group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform duration-300 ${
                            isRTL ? "rtl-flip" : ""
                          }`}
                        />
                      </div>
                    </div>

                    {/* Hover Effect Overlay */}
                    <div className="absolute inset-0 bg-gradient-to-r from-primary-500/5 to-accent-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"></div>
                  </Card>
                </Link>
              </motion.div>
            ))}
          </div>

          {/* Call to Action */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
            className="text-center mt-16"
          >
            <Button
              variant="primary"
              size="lg"
              icon={ArrowRight}
              iconPosition="right"
              href="/services"
              className="shimmer-effect"
            >
              {isRTL ? "استكشف جميع الخدمات" : "Explore All Services"}
            </Button>
          </motion.div>
        </div>
      </section>

      {/* Enhanced Features Section */}
      <section className="py-20 bg-gradient-to-br from-secondary-50 to-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute top-0 left-0 w-72 h-72 bg-primary-200/30 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-accent-200/30 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-4 relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              <div>
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6 }}
                  viewport={{ once: true }}
                  className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-primary-100 text-primary-700 rounded-full px-4 py-2 mb-6"
                >
                  <Star className="w-4 h-4" />
                  <span className="text-sm font-medium">
                    {isRTL ? "لماذا نحن مختلفون" : "Why We're Different"}
                  </span>
                </motion.div>

                <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                  <span className="text-gradient">
                    {isRTL
                      ? "لماذا نحن الخيار الأفضل؟"
                      : "Why Choose NewVision?"}
                  </span>
                </h2>
                <p className="text-xl text-gray-600 leading-relaxed">
                  {isRTL
                    ? "نقدم حلولاً تجارية متكاملة بأعلى معايير الجودة والاحترافية"
                    : "We deliver comprehensive business solutions with the highest standards of quality and professionalism"}
                </p>
              </div>

              <div className="space-y-6">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -30 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-start space-x-4 rtl:space-x-reverse group"
                  >
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <CheckCircle className="w-5 h-5 text-white" />
                    </div>
                    <div className="flex-1">
                      <span className="text-gray-700 font-medium text-lg group-hover:text-gray-900 transition-colors">
                        {feature}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="relative">
                <motion.img
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.3 }}
                  src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Business team"
                  className="rounded-3xl shadow-2xl w-full"
                />

                {/* Floating Rating Card */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.3 }}
                  viewport={{ once: true }}
                  className="absolute -bottom-8 -left-8 bg-gradient-to-r from-primary-400 to-primary-600 text-white p-6 rounded-2xl shadow-xl backdrop-blur-sm"
                >
                  <div className="flex items-center space-x-3 rtl:space-x-reverse">
                    <div className="flex space-x-1">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className="w-5 h-5 fill-current text-yellow-300"
                        />
                      ))}
                    </div>
                    <div>
                      <div className="font-bold text-lg">4.9/5</div>
                      <div className="text-sm opacity-90">
                        {isRTL ? "من عملائنا" : "Client Rating"}
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Floating Stats */}
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.5 }}
                  viewport={{ once: true }}
                  className="absolute -top-6 -right-6 bg-white/90 backdrop-blur-sm p-4 rounded-2xl shadow-lg border border-white/50"
                >
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary-600">
                      15+
                    </div>
                    <div className="text-sm text-gray-600">
                      {isRTL ? "سنة خبرة" : "Years Experience"}
                    </div>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Enhanced CTA Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 via-navy-800 to-secondary-900 relative overflow-hidden">
        {/* Animated Background */}
        <AnimatedBackground variant="gradient" />

        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div
            className="absolute inset-0"
            style={{
              backgroundImage: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E")`,
            }}
          ></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="max-w-4xl mx-auto"
          >
            {/* Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="inline-flex items-center space-x-2 rtl:space-x-reverse bg-white/10 backdrop-blur-sm border border-white/20 rounded-full px-6 py-3 mb-8"
            >
              <Sparkles className="w-5 h-5 text-primary-400" />
              <span className="text-white font-medium">
                {isRTL ? "ابدأ رحلتك معنا" : "Start Your Journey With Us"}
              </span>
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              viewport={{ once: true }}
              className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight"
            >
              <span className="text-gradient-secondary">
                {isRTL
                  ? "هل أنت مستعد لبدء رحلة النجاح؟"
                  : "Ready to Start Your Success Journey?"}
              </span>
            </motion.h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="text-xl md:text-2xl text-gray-200 mb-10 leading-relaxed"
            >
              {isRTL
                ? "تواصل معنا اليوم واحصل على استشارة مجانية لتحقيق أهدافك التجارية"
                : "Contact us today for a free consultation to achieve your business goals"}
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
              className="flex flex-col sm:flex-row justify-center gap-6"
            >
              <Button
                variant="primary"
                size="xl"
                icon={ArrowRight}
                iconPosition="right"
                href="/contact"
                className="shimmer-effect"
              >
                {t("hero.cta")}
              </Button>

              <Button variant="ghost" size="xl" href="/services">
                {t("services.title")}
              </Button>
            </motion.div>

            {/* Trust Indicators */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              viewport={{ once: true }}
              className="flex flex-wrap justify-center items-center gap-8 mt-12 pt-8 border-t border-white/20"
            >
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-white/80">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span className="text-sm">
                  {isRTL ? "استشارة مجانية" : "Free Consultation"}
                </span>
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-white/80">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span className="text-sm">
                  {isRTL ? "خبرة 15+ سنة" : "15+ Years Experience"}
                </span>
              </div>
              <div className="flex items-center space-x-2 rtl:space-x-reverse text-white/80">
                <CheckCircle className="w-5 h-5 text-green-400" />
                <span className="text-sm">
                  {isRTL ? "دعم 24/7" : "24/7 Support"}
                </span>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
