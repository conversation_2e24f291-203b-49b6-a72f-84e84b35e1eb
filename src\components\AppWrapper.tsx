"use client";

import { useLanguage } from "@/contexts/LanguageContext";
import { useEffect } from "react";

export default function AppWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const { language, isRTL } = useLanguage();

  useEffect(() => {
    // Set attributes after hydration to prevent mismatch
    if (typeof window !== 'undefined') {
      document.documentElement.lang = language;
      document.documentElement.dir = isRTL ? "rtl" : "ltr";
    }
  }, [language, isRTL]);

  return <>{children}</>;
}
