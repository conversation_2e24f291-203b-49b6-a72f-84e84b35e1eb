"use client";

import React from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { motion } from "framer-motion";
import {
  Target,
  Eye,
  Heart,
  Award,
  Users,
  TrendingUp,
  CheckCircle,
  Star,
  Shield,
  Lightbulb,
  ArrowDown,
  Home,
  ChevronRight,
  Sparkles,
  Phone,
} from "lucide-react";
import AnimatedBackground from "@/components/ui/AnimatedBackground";
import Button from "@/components/ui/Button";

const AboutPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();

  const values = [
    {
      icon: Shield,
      title: isRTL ? "الثقة والشفافية" : "Trust & Transparency",
      description: isRTL
        ? "نبني علاقات قائمة على الثقة المتبادلة والشفافية الكاملة في جميع تعاملاتنا"
        : "We build relationships based on mutual trust and complete transparency in all our dealings",
    },
    {
      icon: Lightbulb,
      title: isRTL ? "الابتكار والتطوير" : "Innovation & Development",
      description: isRTL
        ? "نسعى دائماً للتطوير والابتكار لتقديم أفضل الحلول العصرية لعملائنا"
        : "We constantly strive for development and innovation to provide the best modern solutions for our clients",
    },
    {
      icon: Award,
      title: isRTL ? "التميز والجودة" : "Excellence & Quality",
      description: isRTL
        ? "نلتزم بأعلى معايير الجودة والتميز في تقديم خدماتنا"
        : "We are committed to the highest standards of quality and excellence in delivering our services",
    },
    {
      icon: Users,
      title: isRTL ? "العمل الجماعي" : "Teamwork",
      description: isRTL
        ? "نؤمن بقوة العمل الجماعي والتعاون لتحقيق أفضل النتائج"
        : "We believe in the power of teamwork and collaboration to achieve the best results",
    },
  ];

  const team = [
    {
      name: isRTL ? "المهندس ايهاب المنسي" : "Engineer Ehab Al-Mansy",
      position: isRTL ? "الرئيس التنفيذي" : "Chief Executive Officer",
      image:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      experience: isRTL ? "20+ سنة خبرة" : "20+ Years Experience",
    },
    {
      name: isRTL ? "الكابتن احمد" : "Kabtin Ahmed",
      position: isRTL ? "مدير العمليات" : "Chief Operations Officer",
      image:
        "https://images.unsplash.com/photo-1494790108755-2616b612b96b?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      experience: isRTL ? "15+ سنة خبرة" : "15+ Years Experience",
    },
    {
      name: isRTL ? " أحمد ايهاب المنسي" : "Ahmed Ehab Al-Mansy",
      position: isRTL ? "مدير التطوير" : "Development Director",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      experience: isRTL ? "12+ سنة خبرة" : "12+ Years Experience",
    },
    {
      name: isRTL ? "سارة محمد القحطاني" : "Sarah Mohammed Al-Qahtani",
      position: isRTL ? "مديرة التسويق" : "Marketing Director",
      image:
        "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
      experience: isRTL ? "10+ سنة خبرة" : "10+ Years Experience",
    },
  ];

  const achievements = [
    { number: "500+", label: isRTL ? "مشروع ناجح" : "Successful Projects" },
    { number: "50+", label: isRTL ? "عميل مؤسسي" : "Corporate Clients" },
    { number: "15+", label: isRTL ? "سنة في السوق" : "Years in Market" },
    {
      number: "95%",
      label: isRTL ? "معدل رضا العملاء" : "Client Satisfaction",
    },
  ];

  return (
    <div className="overflow-hidden">
      {/* Enhanced Hero Section */}
      <section className="relative min-h-screen flex items-center bg-gradient-to-br from-gray-900 via-navy-800 to-blue-900 overflow-hidden">
        {/* Animated Background */}
        <AnimatedBackground variant="particles" />

        {/* Background Image with Multiple Overlays */}
        <div className="absolute inset-0 bg-black/60"></div>
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30"
          style={{
            backgroundImage:
              'url("https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80")',
          }}
        ></div>

        {/* Gradient Overlays */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary-900/20 via-transparent to-accent-900/20"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>

        {/* Floating Elements */}
        <motion.div
          animate={{ y: [0, -20, 0], rotate: [0, 5, 0] }}
          transition={{ duration: 6, repeat: Infinity }}
          className="absolute top-20 right-20 w-20 h-20 bg-primary-400/20 rounded-full blur-xl"
        ></motion.div>
        <motion.div
          animate={{ y: [0, 15, 0], rotate: [0, -3, 0] }}
          transition={{ duration: 8, repeat: Infinity, delay: 2 }}
          className="absolute bottom-32 left-16 w-16 h-16 bg-accent-500/20 rounded-full blur-lg"
        ></motion.div>
        <motion.div
          animate={{ y: [0, -25, 0], x: [0, 10, 0] }}
          transition={{ duration: 10, repeat: Infinity, delay: 4 }}
          className="absolute top-1/3 left-10 w-12 h-12 bg-purple-500/20 rounded-full blur-md"
        ></motion.div>

        <div className="relative container mx-auto px-4 text-white z-10">
          {/* Animated Breadcrumbs */}
          <motion.nav
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="flex items-center space-x-2 mb-8 text-sm text-gray-300"
          >
            <Home className="w-4 h-4" />
            <ChevronRight className="w-4 h-4" />
            <span className="text-primary-400 font-medium">
              {isRTL ? "معلومات عنا" : "About Us"}
            </span>
          </motion.nav>

          <div className="text-center max-w-4xl mx-auto">
            {/* Enhanced Title with Glass Effect */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="relative mb-8"
            >
              <h1 className="text-6xl md:text-7xl lg:text-8xl font-bold mb-4 bg-gradient-to-r from-white via-primary-100 to-accent-300 bg-clip-text text-transparent leading-tight">
                {t("about.title")}
              </h1>
              <motion.div
                animate={{ scale: [1, 1.05, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="absolute -top-4 -right-4 w-8 h-8"
              >
                <Sparkles className="w-8 h-8 text-primary-400" />
              </motion.div>
            </motion.div>

            {/* Enhanced Subtitle with Glass Morphism */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="mb-12"
            >
              <div className="inline-block p-6 rounded-2xl bg-white/10 backdrop-blur-md border border-white/20 shadow-2xl">
                <p className="text-xl md:text-2xl lg:text-3xl text-gray-100 font-light leading-relaxed">
                  {t("about.subtitle")}
                </p>
              </div>
            </motion.div>

            {/* Enhanced Statistics Row */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12"
            >
              {achievements.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  className="bg-white/10 backdrop-blur-md border border-white/20 rounded-xl p-4 text-center hover:bg-white/15 transition-all duration-300"
                >
                  <div className="text-2xl md:text-3xl font-bold text-primary-400 mb-1">
                    {stat.number}
                  </div>
                  <div className="text-sm md:text-base text-gray-300">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        </div>

        {/* Enhanced Scroll Indicator */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1, delay: 1 }}
          className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
            className="flex flex-col items-center space-y-2"
          >
            <span className="text-xs text-gray-300 font-medium">
              {isRTL ? "اكتشف المزيد" : "Discover More"}
            </span>
            <div className="w-8 h-12 border-2 border-white/30 rounded-full flex justify-center backdrop-blur-sm">
              <motion.div
                animate={{ y: [0, 16, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="w-1 h-4 bg-primary-400 rounded-full mt-2"
              ></motion.div>
            </div>
            <ArrowDown className="w-4 h-4 text-primary-400" />
          </motion.div>
        </motion.div>
      </section>

      {/* Company Story */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-6"
            >
              <h2 className="text-4xl font-bold text-gray-900">
                {isRTL ? "قصتنا" : "Our Story"}
              </h2>
              <p className="text-lg text-gray-600 leading-relaxed">
                {isRTL
                  ? "تأسست نيو فيجن في عام 2009 برؤية طموحة لتقديم حلول تجارية متكاملة تلبي احتياجات السوق السعودي المتنامي. بدأنا كشركة صغيرة متخصصة في الوساطة التجارية، ونمت لتصبح واحدة من الشركات الرائدة في أربعة قطاعات حيوية."
                  : "NewVision was founded in 2009 with an ambitious vision to provide comprehensive business solutions that meet the needs of the growing Saudi market. We started as a small company specializing in commercial brokerage and have grown to become one of the leading companies in four vital sectors."}
              </p>
              <p className="text-lg text-gray-600 leading-relaxed">
                {isRTL
                  ? "على مدى السنوات الماضية، توسعنا لنشمل إدارة الأصول والاستثمارات وإنتاج الوسائط، محققين نمواً مستداماً ومبنيين قاعدة عملاء قوية تثق في خبرتنا وقدرتنا على تحقيق النتائج."
                  : "Over the years, we have expanded to include asset management, investments, and media production, achieving sustainable growth and building a strong client base that trusts our expertise and ability to deliver results."}
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <img
                src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                alt="Company building"
                className="rounded-2xl shadow-2xl"
              />
            </motion.div>
          </div>
        </div>
      </section>

      {/* Mission, Vision, Values */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {/* Mission */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg text-center"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Target className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                {t("about.mission")}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {isRTL
                  ? "تقديم حلول تجارية متميزة ومبتكرة تساهم في نمو وازدهار عملائنا وتحقيق أهدافهم الاستراتيجية بأعلى معايير الجودة والاحترافية."
                  : "To provide exceptional and innovative business solutions that contribute to the growth and prosperity of our clients and achieve their strategic goals with the highest standards of quality and professionalism."}
              </p>
            </motion.div>

            {/* Vision */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg text-center"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Eye className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                {t("about.vision")}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {isRTL
                  ? "أن نكون الشريك المفضل والموثوق في المنطقة لتقديم الحلول التجارية المتكاملة، ونساهم في بناء اقتصاد مزدهر ومستدام."
                  : "To be the preferred and trusted partner in the region for providing comprehensive business solutions, contributing to building a prosperous and sustainable economy."}
              </p>
            </motion.div>

            {/* Values */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg text-center"
            >
              <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-6">
                <Heart className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                {t("about.values")}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {isRTL
                  ? "الثقة، التميز، الابتكار، والالتزام هي القيم الأساسية التي توجه عملنا وتحدد هويتنا المؤسسية."
                  : "Trust, excellence, innovation, and commitment are the core values that guide our work and define our corporate identity."}
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Values Grid */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              {isRTL ? "قيمنا الأساسية" : "Our Core Values"}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {isRTL
                ? "القيم التي نؤمن بها وتوجه كل ما نقوم به"
                : "The values we believe in and that guide everything we do"}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center group hover:transform hover:scale-105 transition-all duration-300"
              >
                <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:shadow-lg transition-shadow">
                  <value.icon className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  {value.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {value.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              {isRTL ? "فريق القيادة" : "Leadership Team"}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {isRTL
                ? "خبراء متخصصون يقودون الشركة نحو التميز والنجاح"
                : "Specialized experts leading the company towards excellence and success"}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 group"
              >
                <div className="aspect-square overflow-hidden">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                </div>
                <div className="p-6 text-center">
                  <h3 className="text-xl font-bold text-gray-900 mb-2">
                    {member.name}
                  </h3>
                  <p className="text-yellow-600 font-medium mb-2">
                    {member.position}
                  </p>
                  <p className="text-gray-500 text-sm">{member.experience}</p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Achievements */}
      <section className="py-20 bg-gradient-to-r from-gray-900 to-blue-900">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-white mb-6">
              {isRTL ? "إنجازاتنا" : "Our Achievements"}
            </h2>
            <p className="text-xl text-gray-200 max-w-3xl mx-auto">
              {isRTL
                ? "أرقام تعكس التزامنا بالتميز والجودة"
                : "Numbers that reflect our commitment to excellence and quality"}
            </p>
          </motion.div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-bold text-yellow-400 mb-2">
                  {achievement.number}
                </div>
                <div className="text-gray-200 font-medium">
                  {achievement.label}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default AboutPage;
