"use client";

import React, { useState, useEffect } from "react";
import Link from "next/link";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { Menu, X, Globe, Phone, Mail, Sparkles } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import Button from "@/components/ui/Button";

const Header: React.FC = () => {
  const { t } = useTranslation();
  const { language, toggleLanguage, isRTL } = useLanguage();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  const navigation = [
    { name: t("nav.home"), href: "/" },
    { name: t("nav.about"), href: "/about" },
    { name: t("nav.services"), href: "/services" },
    { name: t("nav.portfolio"), href: "/portfolio" },
    { name: t("nav.blog"), href: "/blog" },
    { name: t("nav.contact"), href: "/contact" },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6 }}
      className={`sticky top-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100"
          : "bg-white shadow-lg"
      }`}
    >
      {/* Enhanced Top bar with improved design and mobile responsiveness */}
      <div className="relative bg-gradient-to-r from-gray-900 via-navy-800 to-gray-900 text-white py-3 overflow-hidden">
        {/* Animated background elements */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 bg-primary-400 rounded-full blur-3xl animate-pulse-soft"></div>
          <div className="absolute top-0 right-0 w-24 h-24 bg-accent-400 rounded-full blur-2xl animate-pulse-soft delay-1000"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          {/* Desktop Layout */}
          <div className="hidden md:flex justify-between items-center text-sm">
            <div className="flex items-center space-x-6 rtl:space-x-reverse">
              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="group flex items-center space-x-2 rtl:space-x-reverse hover:text-primary-400 transition-all duration-300 cursor-pointer"
              >
                <div className="p-2 rounded-full bg-white/10 backdrop-blur-sm group-hover:bg-primary-400/20 transition-all duration-300">
                  <Phone className="w-4 h-4" />
                </div>
                <span className="font-medium group-hover:text-primary-300 transition-colors">
                  +20 1X XXXX XXXX
                </span>
              </motion.div>

              <div className="w-px h-6 bg-white/20"></div>

              <motion.div
                whileHover={{ scale: 1.05, y: -2 }}
                className="group flex items-center space-x-2 rtl:space-x-reverse hover:text-primary-400 transition-all duration-300 cursor-pointer"
              >
                <div className="p-2 rounded-full bg-white/10 backdrop-blur-sm group-hover:bg-primary-400/20 transition-all duration-300">
                  <Mail className="w-4 h-4" />
                </div>
                <span className="font-medium group-hover:text-primary-300 transition-colors">
                  <EMAIL>
                </span>
              </motion.div>
            </div>

            <div className="flex items-center space-x-4 rtl:space-x-reverse">
              <motion.button
                whileHover={{ scale: 1.05, y: -2 }}
                whileTap={{ scale: 0.95 }}
                onClick={toggleLanguage}
                className="group flex items-center space-x-2 rtl:space-x-reverse hover:text-primary-400 transition-all duration-300 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 border border-white/20 hover:border-primary-400/50 hover:bg-white/20 shadow-lg hover:shadow-xl"
              >
                <div className="p-1 rounded-full bg-white/10 group-hover:bg-primary-400/20 transition-all duration-300">
                  <Globe className="w-4 h-4" />
                </div>
                <span className="font-medium text-sm">
                  {language === "en" ? "العربية" : "English"}
                </span>
              </motion.button>
            </div>
          </div>

          {/* Mobile Layout */}
          <div className="md:hidden">
            <div className="flex justify-between items-center">
              {/* Contact Info - Condensed for mobile */}
              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                <motion.a
                  href="tel:+201XXXXXXXX"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="group flex items-center justify-center p-2.5 rounded-full bg-white/10 backdrop-blur-sm hover:bg-primary-400/20 transition-all duration-300 border border-white/20 hover:border-primary-400/50"
                >
                  <Phone className="w-4 h-4 group-hover:text-primary-300 transition-colors" />
                </motion.a>

                <motion.a
                  href="mailto:<EMAIL>"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="group flex items-center justify-center p-2.5 rounded-full bg-white/10 backdrop-blur-sm hover:bg-primary-400/20 transition-all duration-300 border border-white/20 hover:border-primary-400/50"
                >
                  <Mail className="w-4 h-4 group-hover:text-primary-300 transition-colors" />
                </motion.a>
              </div>

              {/* Language Switcher - Compact for mobile */}
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={toggleLanguage}
                className="group flex items-center space-x-1.5 rtl:space-x-reverse bg-white/10 backdrop-blur-sm rounded-full px-3 py-2 border border-white/20 hover:border-primary-400/50 hover:bg-white/20 transition-all duration-300"
              >
                <div className="p-1 rounded-full bg-white/10 group-hover:bg-primary-400/20 transition-all duration-300">
                  <Globe className="w-3.5 h-3.5" />
                </div>
                <span className="font-medium text-xs">
                  {language === "en" ? "عربي" : "EN"}
                </span>
              </motion.button>
            </div>

            {/* Mobile Contact Details - Expandable on tap */}
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              transition={{ duration: 0.3, delay: 0.1 }}
              className="mt-3 pt-3 border-t border-white/10"
            >
              <div className="flex flex-col space-y-2 text-xs">
                <motion.a
                  href="tel:+201XXXXXXXX"
                  whileHover={{ x: 5 }}
                  className="flex items-center space-x-2 rtl:space-x-reverse text-gray-300 hover:text-primary-300 transition-all duration-300"
                >
                  <Phone className="w-3 h-3" />
                  <span>+20 1X XXXX XXXX</span>
                </motion.a>
                <motion.a
                  href="mailto:<EMAIL>"
                  whileHover={{ x: 5 }}
                  className="flex items-center space-x-2 rtl:space-x-reverse text-gray-300 hover:text-primary-300 transition-all duration-300"
                >
                  <Mail className="w-3 h-3" />
                  <span><EMAIL></span>
                </motion.a>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Decorative bottom border with gradient */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-primary-400/50 to-transparent"></div>
      </div>

      {/* Enhanced Main header */}
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Enhanced Logo */}
          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Link
              href="/"
              className="flex items-center space-x-3 rtl:space-x-reverse group"
            >
              <div className="relative">
                <div className="w-12 h-12 bg-gradient-to-r from-primary-400 to-primary-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300">
                  <span className="text-white font-bold text-xl">NV</span>
                </div>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{
                    duration: 20,
                    repeat: Infinity,
                    ease: "linear",
                  }}
                  className="absolute -inset-1 bg-gradient-to-r from-primary-400 to-accent-400 rounded-2xl opacity-20 blur-sm"
                ></motion.div>
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors">
                  NewVision
                </span>
                <span className="text-xs text-gray-600 group-hover:text-gray-700 transition-colors">
                  {language === "en" ? "Business Solutions" : "الحلول التجارية"}
                </span>
              </div>
            </Link>
          </motion.div>

          {/* Enhanced Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 rtl:space-x-reverse">
            {navigation.map((item, index) => (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Link
                  href={item.href}
                  className="text-gray-700 hover:text-primary-600 font-medium transition-all duration-300 relative group py-2 px-1"
                >
                  {item.name}
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-primary-400 to-primary-600 transition-all duration-300 group-hover:w-full rounded-full"></span>
                  <span className="absolute inset-0 bg-primary-50 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10"></span>
                </Link>
              </motion.div>
            ))}
          </nav>

          {/* Enhanced CTA Button & Mobile Menu */}
          <div className="flex items-center space-x-4 rtl:space-x-reverse">
            <div className="hidden md:block">
              <Button
                variant="primary"
                size="md"
                href="/contact"
                className="shimmer-effect"
                icon={Sparkles}
                iconPosition="left"
              >
                {t("hero.cta")}
              </Button>
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="flex lg:hidden p-3 rounded-xl bg-white hover:bg-gray-50 transition-all duration-300 border border-gray-300 hover:border-primary-400 shadow-md text-gray-700 hover:text-primary-600 relative z-50"
              aria-label="Toggle mobile menu"
            >
              <AnimatePresence mode="wait">
                {isMenuOpen ? (
                  <motion.div
                    key="close"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <X className="w-6 h-6" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="menu"
                    initial={{ rotate: 90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: -90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Menu className="w-6 h-6" />
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Enhanced Mobile Menu */}
      <AnimatePresence>
        {isMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0, y: -20 }}
            animate={{ opacity: 1, height: "auto", y: 0 }}
            exit={{ opacity: 0, height: 0, y: -20 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="lg:hidden bg-white/95 backdrop-blur-md border-t border-gray-100 shadow-xl"
          >
            <div className="container mx-auto px-4 py-6">
              <nav className="flex flex-col space-y-2">
                {navigation.map((item, index) => (
                  <motion.div
                    key={item.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                  >
                    <Link
                      href={item.href}
                      onClick={() => setIsMenuOpen(false)}
                      className="text-gray-700 hover:text-primary-600 font-medium py-3 px-4 rounded-xl hover:bg-primary-50 transition-all duration-300 flex items-center justify-between group"
                    >
                      <span>{item.name}</span>
                      <motion.div
                        initial={{ x: -10, opacity: 0 }}
                        whileHover={{ x: 0, opacity: 1 }}
                        className="text-primary-400"
                      >
                        →
                      </motion.div>
                    </Link>
                  </motion.div>
                ))}

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.6 }}
                  className="pt-4 mt-4 border-t border-gray-100"
                >
                  <Button
                    variant="primary"
                    size="lg"
                    href="/contact"
                    onClick={() => setIsMenuOpen(false)}
                    className="w-full shimmer-effect"
                    icon={Sparkles}
                    iconPosition="left"
                  >
                    {t("hero.cta")}
                  </Button>
                </motion.div>
              </nav>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  );
};

export default Header;
