"use client";

import FloatingActionButton from "@/components/ui/FloatingActionButton";
import { useLanguage } from "@/contexts/LanguageContext";

const FloatingActionWrapper = () => {
  const { isRTL } = useLanguage();

  const handleChatClick = () => {
    // Open WhatsApp with pre-filled message in appropriate language
    const phoneNumber = "966XXXXXXXX"; // Replace with actual phone number
    const message = isRTL 
      ? "مرحباً! أود التواصل مع نيو فيجن بخصوص الاستفسارات التجارية."
      : "Hello! I would like to get in touch with NewVision for business inquiries.";
    const encodedMessage = encodeURIComponent(message);
    window.open(`https://wa.me/${phoneNumber}?text=${encodedMessage}`, "_blank");
  };

  const handleCallClick = () => {
    window.open("tel:+201XXXXXXXX");
  };

  return (
    <FloatingActionButton
      onChatClick={handleChatClick}
      onCallClick={handleCallClick}
    />
  );
};

export default FloatingActionWrapper; 