"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useLanguage } from "@/contexts/LanguageContext";
import { motion, AnimatePresence } from "framer-motion";
import { useForm } from "react-hook-form";
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  Send,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  MessageSquare,
  CheckCircle,
  Building,
  Users,
  Award,
  Upload,
  X,
  AlertCircle,
  FileText,
  Calendar,
  Zap,
  User,
  Briefcase,
  Globe,
  Shield,
} from "lucide-react";

interface ContactFormData {
  name: string;
  email: string;
  phone: string;
  countryCode: string;
  subject: string;
  message: string;
  service: string;
  contactMethod: string;
  urgency: string;
  company?: string;
  preferredTime?: string;
  attachments?: FileList;
}

const ContactPage: React.FC = () => {
  const { t } = useTranslation();
  const { isRTL } = useLanguage();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [formProgress, setFormProgress] = useState(0);

  // Note: Particles functionality was removed due to missing type definition

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<ContactFormData>({
    mode: "onChange",
  });

  const watchedFields = watch();

  const contactInfo = [
    {
      icon: Phone,
      title: isRTL ? "الهاتف" : "Phone",
      details: ["+20 1X XXXX XXXX", "+20 1X XXXX XXXX"],
      link: "tel:+966XXXXXXXX",
    },
    {
      icon: Mail,
      title: isRTL ? "البريد الإلكتروني" : "Email",
      details: ["<EMAIL>", "<EMAIL>"],
      link: "mailto:<EMAIL>",
    },
    {
      icon: MapPin,
      title: isRTL ? "العنوان" : "Address",
      details: [
        isRTL ? "ميت غمر، الدقهلية، مصر" : "Mit Ghamr, Dakahlia, Egypt",
        isRTL ? "مبنى نيو فيجن للأعمال" : "NewVision Business Center",
      ],
      link: "#",
    },
    {
      icon: Clock,
      title: isRTL ? "ساعات العمل" : "Working Hours",
      details: [
        isRTL
          ? "الأحد - الخميس: 8:00 - 17:00"
          : "Sunday - Thursday: 8:00 AM - 5:00 PM",
        isRTL ? "الجمعة - السبت: مغلق" : "Friday - Saturday: Closed",
      ],
      link: "#",
    },
  ];

  const socialLinks = [
    { name: "Facebook", icon: Facebook, href: "#", color: "hover:bg-blue-600" },
    { name: "Twitter", icon: Twitter, href: "#", color: "hover:bg-blue-400" },
    { name: "LinkedIn", icon: Linkedin, href: "#", color: "hover:bg-blue-700" },
    {
      name: "Instagram",
      icon: Instagram,
      href: "#",
      color: "hover:bg-pink-500",
    },
  ];

  const serviceOptions = isRTL
    ? [
        "الوساطة التجارية",
        "إدارة الأصول",
        "الاستثمارات والتجارة",
        "الإعلان وإنتاج الوسائط",
        "استشارة عامة",
      ]
    : [
        "Commercial Brokerage",
        "Asset Management",
        "Investments & Trading",
        "Advertising & Media",
        "General Consultation",
      ];

  const contactMethodOptions = isRTL
    ? ["البريد الإلكتروني", "الهاتف", "واتساب", "أي طريقة"]
    : ["Email", "Phone", "WhatsApp", "Any Method"];

  const urgencyOptions = isRTL
    ? ["عادي", "عاجل", "عاجل جداً"]
    : ["Normal", "Urgent", "Very Urgent"];

  const countryOptions = [
    { name: isRTL ? "مصر" : "Egypt", code: "+20" },
    { name: isRTL ? "المملكة العربية السعودية" : "Saudi Arabia", code: "+966" },
    { name: isRTL ? "الإمارات العربية المتحدة" : "UAE", code: "+971" },
    { name: isRTL ? "الكويت" : "Kuwait", code: "+965" },
    { name: isRTL ? "البحرين" : "Bahrain", code: "+973" },
    { name: isRTL ? "قطر" : "Qatar", code: "+974" },
    { name: isRTL ? "عُمان" : "Oman", code: "+968" },
    { name: isRTL ? "الأردن" : "Jordan", code: "+962" },
    { name: isRTL ? "لبنان" : "Lebanon", code: "+961" },
    { name: isRTL ? "سوريا" : "Syria", code: "+963" },
    { name: isRTL ? "العراق" : "Iraq", code: "+964" },
    { name: isRTL ? "المغرب" : "Morocco", code: "+212" },
    { name: isRTL ? "الجزائر" : "Algeria", code: "+213" },
    { name: isRTL ? "تونس" : "Tunisia", code: "+216" },
    { name: isRTL ? "ليبيا" : "Libya", code: "+218" },
    { name: "United States", code: "+1" },
    { name: "United Kingdom", code: "+44" },
    { name: "Germany", code: "+49" },
    { name: "France", code: "+33" },
    { name: "Italy", code: "+39" },
    { name: "Spain", code: "+34" },
    { name: "Turkey", code: "+90" },
    { name: "India", code: "+91" },
    { name: "China", code: "+86" },
    { name: "Japan", code: "+81" },
    { name: "Canada", code: "+1" },
    { name: "Australia", code: "+61" },
  ];

  // File upload handlers
  const handleFileUpload = useCallback((files: FileList | null) => {
    if (!files) return;

    const newFiles = Array.from(files).filter((file) => {
      const isValidType = file.type.match(
        /^(image|application\/pdf|text|application\/msword|application\/vnd\.openxmlformats-officedocument\.wordprocessingml\.document)/
      );
      const isValidSize = file.size <= 10 * 1024 * 1024; // 10MB
      return isValidType && isValidSize;
    });

    setUploadedFiles((prev) => [...prev, ...newFiles].slice(0, 5)); // Max 5 files
  }, []);

  const removeFile = useCallback((index: number) => {
    setUploadedFiles((prev) => prev.filter((_, i) => i !== index));
  }, []);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setDragActive(false);
      handleFileUpload(e.dataTransfer.files);
    },
    [handleFileUpload]
  );

  // Calculate form progress
  React.useEffect(() => {
    const requiredFields = ["name", "email", "message"];
    const optionalFields = [
      "phone",
      "countryCode",
      "subject",
      "service",
      "contactMethod",
      "urgency",
      "company",
      "preferredTime",
    ];
    const allFields = [...requiredFields, ...optionalFields];

    const filledFields = allFields.filter((field) => {
      const value = watchedFields[field as keyof ContactFormData];
      return value && value.toString().trim() !== "";
    }).length;

    const progress = Math.round((filledFields / allFields.length) * 100);
    setFormProgress(progress);
  }, [watchedFields]);

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);

    // Combine country code and phone number
    const fullPhoneNumber =
      data.countryCode && data.phone
        ? `${data.countryCode}${data.phone}`
        : data.phone;

    // Include uploaded files in submission
    const formData = new FormData();
    Object.entries(data).forEach(([key, value]) => {
      if (value) {
        // Use combined phone number instead of separate fields
        if (key === "phone") {
          formData.append(key, fullPhoneNumber);
        } else if (key !== "countryCode") {
          formData.append(key, value);
        }
      }
    });

    uploadedFiles.forEach((file, index) => {
      formData.append(`attachment_${index}`, file);
    });

    // Simulate form submission with progress
    for (let i = 0; i <= 100; i += 10) {
      await new Promise((resolve) => setTimeout(resolve, 100));
      // Could update a progress bar here
    }

    console.log("Form submitted:", { ...data, fullPhoneNumber });
    console.log("Uploaded files:", uploadedFiles);
    setIsSubmitted(true);
    setIsSubmitting(false);
    reset();
    setUploadedFiles([]);
    setFormProgress(0);

    // Reset success message after 5 seconds
    setTimeout(() => setIsSubmitted(false), 5000);
  };

  const features = [
    {
      icon: Building,
      title: isRTL ? "مكاتب حديثة" : "Modern Offices",
      description: isRTL
        ? "مرافق عصرية ومجهزة بأحدث التقنيات"
        : "Modern facilities equipped with latest technology",
    },
    {
      icon: Users,
      title: isRTL ? "فريق خبير" : "Expert Team",
      description: isRTL
        ? "خبراء متخصصون في جميع المجالات"
        : "Specialized experts in all fields",
    },
    {
      icon: Award,
      title: isRTL ? "خدمة متميزة" : "Premium Service",
      description: isRTL
        ? "التزام بأعلى معايير الجودة"
        : "Commitment to highest quality standards",
    },
  ];

  return (
    <div className="overflow-hidden">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-gray-800">
        <div className="absolute inset-0 bg-black opacity-50"></div>
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage:
              'url("https://images.unsplash.com/photo-1486406146494-da3fb1916ddf?ixlib=rb-4.0.3&auto=format&fit=crop&w=2000&q=80")',
          }}
        ></div>

        <div className="relative container mx-auto px-4 text-center text-white">
          <motion.h1
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-5xl md:text-6xl font-bold mb-6"
          >
            {t("contact.title")}
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-xl md:text-2xl text-gray-200 max-w-3xl mx-auto"
          >
            {t("contact.subtitle")}
          </motion.p>
        </div>
      </section>

      {/* Contact Information Cards */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              {isRTL ? "معلومات الاتصال" : "Contact Information"}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {isRTL
                ? "نحن هنا لخدمتكم ومساعدتكم في تحقيق أهدافكم التجارية"
                : "We are here to serve you and help you achieve your business goals"}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {contactInfo.map((info, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 text-center group"
              >
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform">
                  <info.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-4">
                  {info.title}
                </h3>
                <div className="space-y-2">
                  {info.details.map((detail, idx) => (
                    <p key={idx} className="text-gray-600">
                      {detail}
                    </p>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form & Map Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="bg-white rounded-2xl p-8 shadow-lg"
            >
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-3xl font-bold text-gray-900">
                  {isRTL ? "أرسل لنا رسالة" : "Send us a Message"}
                </h3>
                <div className="flex items-center space-x-2 rtl:space-x-reverse">
                  <div className="w-24 bg-gray-200 rounded-full h-2">
                    <motion.div
                      className="bg-gradient-to-r from-yellow-400 to-yellow-600 h-2 rounded-full"
                      initial={{ width: "0%" }}
                      animate={{ width: `${formProgress}%` }}
                      transition={{ duration: 0.3 }}
                    />
                  </div>
                  <span className="text-sm text-gray-500">{formProgress}%</span>
                </div>
              </div>

              <AnimatePresence>
                {isSubmitted && (
                  <motion.div
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-center space-x-3 rtl:space-x-reverse"
                  >
                    <CheckCircle className="w-5 h-5 text-green-500" />
                    <span className="text-green-700">
                      {isRTL
                        ? "تم إرسال رسالتك بنجاح! سنتواصل معك قريباً."
                        : "Your message has been sent successfully! We will contact you soon."}
                    </span>
                  </motion.div>
                )}
              </AnimatePresence>

              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                {/* Personal Information */}
                <div className="border-l-4 border-yellow-400 pl-4 mb-6">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <User className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                    {isRTL ? "المعلومات الشخصية" : "Personal Information"}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.1 }}
                    >
                      <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        {t("contact.form.name")} *
                        <span className="text-red-500 ml-1">*</span>
                      </label>
                      <div className="relative">
                        <input
                          type="text"
                          {...register("name", {
                            required: isRTL
                              ? "الاسم مطلوب"
                              : "Name is required",
                            minLength: {
                              value: 2,
                              message: isRTL
                                ? "الاسم يجب أن يكون حرفين على الأقل"
                                : "Name must be at least 2 characters",
                            },
                          })}
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 text-gray-900 ${
                            errors.name
                              ? "border-red-500 bg-red-50"
                              : "border-gray-300 hover:border-gray-400"
                          }`}
                          placeholder={
                            isRTL ? "أدخل اسمك الكامل" : "Enter your full name"
                          }
                        />
                        {errors.name && (
                          <AlertCircle className="w-5 h-5 text-red-500 absolute right-3 top-3" />
                        )}
                      </div>
                      <AnimatePresence>
                        {errors.name && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="text-red-500 text-sm mt-1 flex items-center"
                          >
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {errors.name.message}
                          </motion.p>
                        )}
                      </AnimatePresence>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                        {t("contact.form.email")} *
                        <span className="text-red-500 ml-1">*</span>
                      </label>
                      <div className="relative">
                        <input
                          type="email"
                          {...register("email", {
                            required: isRTL
                              ? "البريد الإلكتروني مطلوب"
                              : "Email is required",
                            pattern: {
                              value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                              message: isRTL
                                ? "البريد الإلكتروني غير صحيح"
                                : "Invalid email address",
                            },
                          })}
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 text-gray-900 ${
                            errors.email
                              ? "border-red-500 bg-red-50"
                              : "border-gray-300 hover:border-gray-400"
                          }`}
                          placeholder={
                            isRTL
                              ? "أدخل بريدك الإلكتروني"
                              : "Enter your email address"
                          }
                        />
                        {errors.email && (
                          <AlertCircle className="w-5 h-5 text-red-500 absolute right-3 top-3" />
                        )}
                      </div>
                      <AnimatePresence>
                        {errors.email && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="text-red-500 text-sm mt-1 flex items-center"
                          >
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {errors.email.message}
                          </motion.p>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  </div>
                </div>

                {/* Contact & Business Information */}
                <div className="border-l-4 border-blue-400 pl-4 mb-6">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <Briefcase className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                    {isRTL
                      ? "معلومات الاتصال والعمل"
                      : "Contact & Business Information"}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? "رقم الهاتف" : "Phone Number"}
                      </label>
                      <div className="grid grid-cols-5 gap-3">
                        {/* Country Code Selector */}
                        <div className="col-span-2">
                          <select
                            {...register("countryCode", {
                              required: isRTL
                                ? "كود الدولة مطلوب"
                                : "Country code is required",
                            })}
                            className={`w-full px-3 py-3 border rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 bg-white text-gray-900 text-sm ${
                              errors.countryCode
                                ? "border-red-500 bg-red-50"
                                : "border-gray-300 hover:border-gray-400"
                            }`}
                          >
                            <option value="">
                              {isRTL ? "اختر الدولة" : "Select Country"}
                            </option>
                            {countryOptions.map((country, index) => (
                              <option key={index} value={country.code}>
                                {country.name} ({country.code})
                              </option>
                            ))}
                          </select>
                        </div>

                        {/* Phone Number Input */}
                        <div className="col-span-3">
                          <div className="relative">
                            <input
                              type="tel"
                              {...register("phone", {
                                required: isRTL
                                  ? "رقم الهاتف مطلوب"
                                  : "Phone number is required",
                                pattern: {
                                  value: /^[1-9][\d]{6,14}$/,
                                  message: isRTL
                                    ? "رقم الهاتف غير صحيح"
                                    : "Invalid phone number",
                                },
                              })}
                              className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 text-gray-900 ${
                                errors.phone
                                  ? "border-red-500 bg-red-50"
                                  : "border-gray-300 hover:border-gray-400"
                              }`}
                              placeholder={isRTL ? "123456789" : "123456789"}
                            />
                            {(errors.phone || errors.countryCode) && (
                              <AlertCircle className="w-5 h-5 text-red-500 absolute right-3 top-3" />
                            )}
                          </div>
                        </div>
                      </div>
                      <AnimatePresence>
                        {(errors.phone || errors.countryCode) && (
                          <motion.p
                            initial={{ opacity: 0, y: -10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: -10 }}
                            className="text-red-500 text-sm mt-1 flex items-center"
                          >
                            <AlertCircle className="w-4 h-4 mr-1" />
                            {errors.countryCode?.message ||
                              errors.phone?.message}
                          </motion.p>
                        )}
                      </AnimatePresence>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.4 }}
                    >
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? "اسم الشركة" : "Company Name"}
                      </label>
                      <input
                        type="text"
                        {...register("company")}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 text-gray-900"
                        placeholder={
                          isRTL
                            ? "اسم شركتك (اختياري)"
                            : "Your company name (optional)"
                        }
                      />
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.5 }}
                    >
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? "الخدمة المطلوبة" : "Service Required"}
                      </label>
                      <select
                        {...register("service")}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 bg-white text-gray-900"
                      >
                        <option value="">
                          {isRTL ? "اختر الخدمة" : "Select Service"}
                        </option>
                        {serviceOptions.map((option, index) => (
                          <option key={index} value={option}>
                            {option}
                          </option>
                        ))}
                      </select>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.6 }}
                    >
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL ? "مستوى الأولوية" : "Urgency Level"}
                      </label>
                      <select
                        {...register("urgency")}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 bg-white text-gray-900"
                      >
                        <option value="">
                          {isRTL
                            ? "اختر مستوى الأولوية"
                            : "Select urgency level"}
                        </option>
                        {urgencyOptions.map((option, index) => (
                          <option key={index} value={option}>
                            {option === "Normal" && "🟢"}
                            {option === "Urgent" && "🟡"}
                            {option === "Very Urgent" && "🔴"}
                            {option === "عادي" && "🟢"}
                            {option === "عاجل" && "🟡"}
                            {option === "عاجل جداً" && "🔴"}
                            {" " + option}
                          </option>
                        ))}
                      </select>
                    </motion.div>
                  </div>
                </div>

                {/* Contact Preferences */}
                <div className="border-l-4 border-green-400 pl-4 mb-6">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <Globe className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                    {isRTL ? "تفضيلات الاتصال" : "Contact Preferences"}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.7 }}
                    >
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL
                          ? "طريقة الاتصال المفضلة"
                          : "Preferred Contact Method"}
                      </label>
                      <select
                        {...register("contactMethod")}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 bg-white text-gray-900"
                      >
                        <option value="">
                          {isRTL
                            ? "اختر طريقة الاتصال"
                            : "Select contact method"}
                        </option>
                        {contactMethodOptions.map((option, index) => (
                          <option key={index} value={option}>
                            {option === "Email" && "📧"}
                            {option === "Phone" && "📞"}
                            {option === "WhatsApp" && "💬"}
                            {option === "Any Method" && "🤝"}
                            {option === "البريد الإلكتروني" && "📧"}
                            {option === "الهاتف" && "📞"}
                            {option === "واتساب" && "💬"}
                            {option === "أي طريقة" && "🤝"}
                            {" " + option}
                          </option>
                        ))}
                      </select>
                    </motion.div>

                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.8 }}
                    >
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        {isRTL
                          ? "الوقت المفضل للاتصال"
                          : "Preferred Contact Time"}
                      </label>
                      <select
                        {...register("preferredTime")}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 bg-white text-gray-900"
                      >
                        <option value="">
                          {isRTL
                            ? "اختر الوقت المفضل"
                            : "Select preferred time"}
                        </option>
                        <option value="morning">
                          🌅{" "}
                          {isRTL
                            ? "صباحاً (8:00 - 12:00)"
                            : "Morning (8:00 AM - 12:00 PM)"}
                        </option>
                        <option value="afternoon">
                          ☀️{" "}
                          {isRTL
                            ? "بعد الظهر (12:00 - 17:00)"
                            : "Afternoon (12:00 PM - 5:00 PM)"}
                        </option>
                        <option value="evening">
                          🌆{" "}
                          {isRTL
                            ? "مساءً (17:00 - 20:00)"
                            : "Evening (5:00 PM - 8:00 PM)"}
                        </option>
                        <option value="anytime">
                          🕐 {isRTL ? "أي وقت" : "Anytime"}
                        </option>
                      </select>
                    </motion.div>
                  </div>
                </div>

                {/* Message Section */}
                <div className="border-l-4 border-purple-400 pl-4 mb-6">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <MessageSquare className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                    {isRTL ? "تفاصيل الرسالة" : "Message Details"}
                  </h4>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.9 }}
                    className="mb-6"
                  >
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {isRTL ? "الموضوع" : "Subject"}
                    </label>
                    <input
                      type="text"
                      {...register("subject")}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 hover:border-gray-400 text-gray-900"
                      placeholder={isRTL ? "موضوع الرسالة" : "Message subject"}
                      maxLength={100}
                    />
                    <div className="text-right text-xs text-gray-400 mt-1">
                      {watchedFields.subject?.length || 0}/100
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.0 }}
                  >
                    <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                      {t("contact.form.message")} *
                      <span className="text-red-500 ml-1">*</span>
                    </label>
                    <div className="relative">
                      <textarea
                        rows={5}
                        {...register("message", {
                          required: isRTL
                            ? "الرسالة مطلوبة"
                            : "Message is required",
                          minLength: {
                            value: 10,
                            message: isRTL
                              ? "الرسالة يجب أن تكون 10 أحرف على الأقل"
                              : "Message must be at least 10 characters",
                          },
                        })}
                        className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-transparent transition-all duration-200 resize-none text-gray-900 ${
                          errors.message
                            ? "border-red-500 bg-red-50"
                            : "border-gray-300 hover:border-gray-400"
                        }`}
                        placeholder={
                          isRTL
                            ? "اكتب رسالتك هنا... أخبرنا عن احتياجاتك وكيف يمكننا مساعدتك"
                            : "Write your message here... Tell us about your needs and how we can help you"
                        }
                        maxLength={1000}
                      ></textarea>
                      {errors.message && (
                        <AlertCircle className="w-5 h-5 text-red-500 absolute right-3 top-3" />
                      )}
                    </div>
                    <div className="flex justify-between text-xs text-gray-400 mt-1">
                      <AnimatePresence>
                        {errors.message && (
                          <motion.span
                            initial={{ opacity: 0, x: -10 }}
                            animate={{ opacity: 1, x: 0 }}
                            exit={{ opacity: 0, x: -10 }}
                            className="text-red-500 flex items-center"
                          >
                            <AlertCircle className="w-3 h-3 mr-1" />
                            {errors.message.message}
                          </motion.span>
                        )}
                      </AnimatePresence>
                      <span>{watchedFields.message?.length || 0}/1000</span>
                    </div>
                  </motion.div>
                </div>

                {/* File Upload Section */}
                <div className="border-l-4 border-orange-400 pl-4 mb-6">
                  <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                    <FileText className="w-5 h-5 mr-2 rtl:ml-2 rtl:mr-0" />
                    {isRTL ? "المرفقات" : "Attachments"}
                    <span className="text-sm font-normal text-gray-500 ml-2">
                      ({isRTL ? "اختياري" : "Optional"})
                    </span>
                  </h4>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.1 }}
                  >
                    <div
                      className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 ${
                        dragActive
                          ? "border-yellow-400 bg-yellow-50"
                          : "border-gray-300 hover:border-gray-400"
                      }`}
                      onDragEnter={handleDrag}
                      onDragLeave={handleDrag}
                      onDragOver={handleDrag}
                      onDrop={handleDrop}
                    >
                      <input
                        type="file"
                        multiple
                        accept="image/*,.pdf,.doc,.docx,.txt"
                        onChange={(e) => handleFileUpload(e.target.files)}
                        className="hidden"
                        id="file-upload"
                      />
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-gray-600 mb-1">
                          {isRTL
                            ? "اسحب وأفلت الملفات هنا أو انقر للتحديد"
                            : "Drag and drop files here or click to select"}
                        </p>
                        <p className="text-xs text-gray-400">
                          {isRTL
                            ? "PNG, JPG, PDF, DOC, TXT - حد أقصى 10MB لكل ملف - 5 ملفات كحد أقصى"
                            : "PNG, JPG, PDF, DOC, TXT - Max 10MB per file - Up to 5 files"}
                        </p>
                      </label>
                    </div>

                    {/* Uploaded Files Display */}
                    <AnimatePresence>
                      {uploadedFiles.length > 0 && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          className="mt-4 space-y-2"
                        >
                          {uploadedFiles.map((file, index) => (
                            <motion.div
                              key={index}
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              exit={{ opacity: 0, x: 20 }}
                              className="flex items-center justify-between bg-gray-50 p-3 rounded-lg"
                            >
                              <div className="flex items-center space-x-3 rtl:space-x-reverse">
                                <FileText className="w-5 h-5 text-gray-500" />
                                <div>
                                  <p className="text-sm font-medium text-gray-700 truncate max-w-48">
                                    {file.name}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    {(file.size / 1024 / 1024).toFixed(2)} MB
                                  </p>
                                </div>
                              </div>
                              <button
                                type="button"
                                onClick={() => removeFile(index)}
                                className="text-red-500 hover:text-red-700 transition-colors"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </motion.div>
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.div>
                </div>

                {/* Enhanced Submit Button */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 1.2 }}
                >
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-black font-semibold py-4 px-6 rounded-lg hover:from-yellow-500 hover:to-yellow-700 transition-all transform hover:scale-105 flex items-center justify-center space-x-2 rtl:space-x-reverse disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black"></div>
                        <span className="ml-2 rtl:mr-2 rtl:ml-0">
                          {isRTL ? "جاري الإرسال..." : "Sending..."}
                        </span>
                      </>
                    ) : (
                      <>
                        <Shield className="w-5 h-5" />
                        <span>{t("contact.form.submit")}</span>
                        <Send
                          className={`w-5 h-5 ${isRTL ? "rtl-flip" : ""}`}
                        />
                      </>
                    )}
                  </button>

                  <div className="mt-4 text-center">
                    <p className="text-xs text-gray-500 flex items-center justify-center">
                      <Shield className="w-3 h-3 mr-1" />
                      {isRTL
                        ? "نحن نحترم خصوصيتك ونحافظ على سرية معلوماتك"
                        : "We respect your privacy and keep your information secure"}
                    </p>
                  </div>
                </motion.div>
              </form>
            </motion.div>

            {/* Map & Additional Info */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="space-y-8"
            >
              {/* Map */}
              <div className="bg-white rounded-2xl p-4 shadow-lg">
                <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">
                      {isRTL ? "خريطة الموقع" : "Location Map"}
                    </p>
                    <p className="text-sm text-gray-400 mt-1">
                      {isRTL
                        ? "ميت غمر، الدقهلية، مصر"
                        : "Mit Ghamr, Dakahlia, Egypt"}
                    </p>
                  </div>
                </div>
              </div>

              {/* Features */}
              <div className="space-y-6">
                {features.map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="bg-white rounded-lg p-6 shadow-lg flex items-start space-x-4 rtl:space-x-reverse"
                  >
                    <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-yellow-600 rounded-lg flex items-center justify-center flex-shrink-0">
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h4 className="text-lg font-bold text-gray-900 mb-2">
                        {feature.title}
                      </h4>
                      <p className="text-gray-600">{feature.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>

              {/* Social Media */}
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <h4 className="text-xl font-bold text-gray-900 mb-4">
                  {isRTL ? "تابعنا على" : "Follow Us"}
                </h4>
                <div className="flex space-x-4 rtl:space-x-reverse">
                  {socialLinks.map(({ name, icon: Icon, href, color }) => (
                    <a
                      key={name}
                      href={href}
                      className={`w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center transition-colors ${color}`}
                      aria-label={name}
                    >
                      <Icon className="w-5 h-5 text-gray-600 hover:text-white transition-colors" />
                    </a>
                  ))}
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* WhatsApp CTA */}
      <section className="py-16 bg-green-600">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="max-w-2xl mx-auto"
          >
            <h3 className="text-3xl font-bold text-white mb-4">
              {isRTL ? "تحدث معنا مباشرة" : "Chat with Us Directly"}
            </h3>
            <p className="text-green-100 mb-6">
              {isRTL
                ? "للحصول على رد سريع، تواصل معنا عبر الواتساب"
                : "For quick response, contact us via WhatsApp"}
            </p>
            <a
              href="https://wa.me/966XXXXXXXX"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center bg-white text-green-600 px-8 py-4 rounded-lg font-semibold hover:bg-green-50 transition-all transform hover:scale-105 space-x-2 rtl:space-x-reverse"
            >
              <MessageSquare className="w-5 h-5" />
              <span>
                {isRTL ? "راسلنا على الواتساب" : "Message us on WhatsApp"}
              </span>
            </a>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ContactPage;
